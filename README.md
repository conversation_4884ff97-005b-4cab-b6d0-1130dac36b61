# First Share 导航

一个现代化的企业级导航页面，基于原生JavaScript构建，提供智能搜索、多主题切换、响应式设计和完整的状态管理功能。

![NavSphere](https://img.shields.io/badge/NavSphere-v2.0-blue) ![License](https://img.shields.io/badge/license-MIT-green) ![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow) ![CSS](https://img.shields.io/badge/CSS-Grid%20%26%20Flexbox-blue) ![PWA](https://img.shields.io/badge/PWA-Ready-purple)

## 📖 目录

- [核心特性](#-核心特性)
- [技术架构](#-技术架构)
- [快速开始](#-快速开始)
- [配置指南](#-配置指南)
- [时间范围提示配置](#-时间范围提示配置)
- [功能详解](#-功能详解)
- [主题系统](#-主题系统)
- [开发指南](#-开发指南)
- [部署方案](#-部署方案)
- [性能优化](#-性能优化)
- [故障排除](#-故障排除)
- [API文档](#-api文档)
- [时间范围提示API](#时间范围提示api)
- [贡献指南](#-贡献指南)

## 🌟 核心特性

### 🎯 智能导航系统
- **多级分类结构** - 支持3级分类嵌套，灵活的内容组织方式
- **智能状态记忆** - 自动保存分类展开状态和当前浏览位置
- **访问统计** - 内置访问记录管理，支持使用频率分析
- **Markdown文档** - 支持本地文档渲染，丰富的内容展示
- **🔗 多链接卡片** - 每个卡片支持网址链接和文档链接的灵活组合

### 🔍 高级搜索引擎
- **实时搜索** - 300ms防抖优化，支持多字段匹配
- **智能权重** - 标题、描述、标签、分类的差异化权重排序
- **标签筛选** - 支持标签精确匹配和部分匹配，高亮显示
- **快捷键支持** - `⌘K`/`Ctrl+K` 快速唤起，键盘导航

### � 多主题系统
- **4套精美主题** - 日光象牙白、夜月玄玉黑、清雅茉莉绿、深邃海军蓝
- **时间自动切换** - 可配置时间段自动切换主题
- **系统偏好跟随** - 自动检测系统深色模式偏好
- **平滑过渡动画** - 主题切换无闪烁，视觉体验流畅

### ⏰ 时间范围提示系统
- **智能时间检测** - 支持每日时间范围和绝对时间范围两种模式
- **配置文件驱动** - 通过JSON配置文件管理所有提示设置
- **预设配置** - 内置学生作息、办公室工作等预设配置
- **非阻塞提示** - Toast风格提示，不影响用户正常操作
- **首次检测机制** - 基于时间范围和内容的智能首次提示判定

### 📱 响应式设计
- **移动端优化** - 抽屉式侧边栏，触摸友好的交互设计
- **自适应布局** - CSS Grid + Flexbox，完美适配各种屏幕
- **性能优化** - 懒加载、防抖搜索、事件委托等优化策略

## 🏗️ 技术架构

### 📁 项目结构

```
fs-oss-navigation/
├── index.html              # 主页面入口
├── css/                    # 样式系统
│   ├── style.css          # 核心样式和CSS变量
│   ├── themes.css         # 多主题样式定义
│   └── responsive.css     # 响应式布局
├── js/                     # JavaScript模块
│   ├── app.js             # 主应用控制器
│   ├── search.js          # 搜索引擎
│   ├── theme.js           # 主题管理器
│   ├── sidebar.js         # 侧边栏管理器
│   ├── markdown.js        # Markdown渲染器
│   ├── visit-manager.js   # 访问统计管理
│   ├── time-notification.js # 时间范围提示管理器
│   ├── config.js          # 全局配置
│   ├── platform.js        # 平台检测工具
│   └── utils.js           # 通用工具函数
├── data/                   # 数据层
│   ├── appconfig.json     # 应用核心配置
│   ├── sites.json         # 网站数据配置
│   ├── time-notifications.json # 时间范围提示配置
│   └── docs/              # Markdown文档
├── assets/                 # 静态资源
│   └── icons/             # 图标资源
└── README.md              # 项目文档
```

### 🔧 核心架构

```
┌─────────────────────────────────────────┐
│                NavApp                   │  ← 主应用控制器
├─────────────────────────────────────────┤
│  ThemeManager  │  SearchManager         │  ← 功能管理器
│  SidebarManager│  MarkdownManager       │
│  VisitManager  │  TimeNotificationMgr   │
│  Platform Utils│  Config Manager        │
├─────────────────────────────────────────┤
│  DOM操作层     │  事件处理层            │  ← 底层服务
│  数据处理层    │  工具函数层            │
└─────────────────────────────────────────┘
```

### 🎯 设计模式

- **单例模式** - 主应用实例和管理器实例
- **观察者模式** - 主题变化通知和状态更新
- **策略模式** - 主题切换策略和搜索匹配策略
- **工厂模式** - DOM元素创建和事件处理器
- **模块模式** - 功能封装和命名空间管理

## 🚀 快速开始

### 📦 环境要求

- **现代浏览器** - Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **本地服务器** - Python/Node.js/PHP 任一（开发时）
- **无框架依赖** - 纯原生JavaScript实现

### 🛠️ 本地运行

1. **克隆项目**

   ```bash
   git clone https://github.com/your-repo/fs-oss-navigation.git
   cd fs-oss-navigation
   ```

2. **启动本地服务器**

   ```bash
   # 使用Python (推荐)
   python -m http.server 8000

   # 使用Node.js
   npx serve . -p 8000

   # 使用PHP
   php -S localhost:8000
   ```

3. **访问应用**

   打开浏览器访问 `http://localhost:8000`

### 🌐 部署方案

#### 静态托管平台

- **GitHub Pages** - 免费，支持自定义域名
- **Vercel** - 全球CDN，自动部署
- **Netlify** - 表单处理，边缘函数
- **阿里云OSS** - 国内访问优化
- **腾讯云COS** - 企业级存储

#### 一键部署

```bash
# Vercel部署
npx vercel --prod

# Netlify部署
npx netlify-cli deploy --prod --dir .
```

## ⚙️ 配置指南

### 🎨 主题系统配置

#### 可用主题

| 主题名称 | 主题ID | 特色描述 |
|---------|--------|----------|
| 日光象牙白 | `ivory-light` | 温暖舒适的浅色主题 |
| 夜月玄玉黑 | `dark-obsidian` | 深邃优雅的深色主题 |
| 清雅茉莉绿 | `jasmine-green` | 茉莉花香·禅意美学·护眼悦目 |
| 深邃海军蓝 | `navy-blue` | 沉稳专业的蓝色主题 |

#### 时间主题配置

编辑 `js/config.js` 文件：

```javascript
window.NavSphereConfig = {
    timeTheme: {
        enabled: true,           // 启用时间主题
        lightTheme: 'ivory-light',  // 浅色时段使用的主题
        darkTheme: 'dark-obsidian', // 深色时段使用的主题
        lightStart: 6,           // 浅色主题开始时间 (6:00)
        lightEnd: 18,            // 浅色主题结束时间 (18:00)
    }
};
```

#### 预设配置

| 预设名称 | 浅色时间段 | 适用场景 |
|---------|-----------|----------|
| `standard` | 6:00-18:00 | 标准作息 (默认) |
| `earlyBird` | 5:00-19:00 | 早起型用户 |
| `nightOwl` | 8:00-20:00 | 夜猫子型用户 |
| `office` | 9:00-17:00 | 办公时间 |

#### 快速配置API

```javascript
// 应用预设
applyTimeThemePreset("nightOwl")

// 自定义时间段
NavSphereQuickConfig.setTimeTheme(7, 19)

// 手动切换主题
NavApp.theme.setTheme('jasmine-green')

// 查看当前配置
NavApp.theme.getInfo()
```

### 📊 数据配置

#### 多文件配置系统

从 v2.0 开始，支持通过 `nav/data/appconfig.json` 配置多个数据源文件：

```json
{
  "version": "2.0",
  "description": "FaciShare 导航数据配置",
  "dataSources": [
    {
      "id": "main",
      "name": "主要网站数据",
      "path": "data/sites.json",
      "enabled": true,
      "priority": 1,
      "description": "包含主要的企业内部工具和服务"
    },
    {
      "id": "external",
      "name": "外部工具数据",
      "path": "data/external-sites.json",
      "enabled": true,
      "priority": 2,
      "description": "第三方工具和外部服务"
    }
  ],
  "mergeStrategy": {
    "duplicateHandling": "merge",
    "categoryMerging": "append",
    "siteIdConflict": "keepFirst"
  }
}
```

**配置说明**：
- `dataSources`: 数据源配置数组
  - `id`: 数据源唯一标识
  - `name`: 数据源显示名称
  - `path`: JSON文件路径（相对于项目根目录）
  - `enabled`: 是否启用此数据源
  - `priority`: 加载优先级（数字越小优先级越高）
  - `description`: 数据源描述
- `mergeStrategy`: 数据合并策略
  - `duplicateHandling`: 重复数据处理方式（`merge`/`skip`/`replace`）
  - `categoryMerging`: 分类合并方式（`append`/`merge`）
  - `siteIdConflict`: 网站ID冲突处理（`keepFirst`/`keepLast`/`merge`）

#### 基础网站配置

编辑 `data/sites.json` 文件：

```json
{
  "categories": [
    {
      "id": "development",
      "name": "开发工具",
      "icon": "💻",
      "description": "开发相关的工具和资源",
      "sites": [
        {
          "id": "vscode",
          "name": "Visual Studio Code",
          "description": "微软开发的免费代码编辑器",
          "icon": "📝",
          "url": "https://code.visualstudio.com",
          "tags": ["编辑器", "开发", "微软", "免费"],
          "featured": true
        }
      ]
    }
  ]
}
```

#### 多级分类结构

```json
{
  "id": "programming",
  "name": "编程学习",
  "icon": "📚",
  "children": [
    {
      "id": "languages",
      "name": "编程语言",
      "children": [
        {
          "id": "javascript",
          "name": "JavaScript",
          "sites": []
        }
      ]
    }
  ]
}
```

#### 字段说明

**分类字段**：

- `id` (必需) - 唯一标识符，用于URL和内部引用
- `name` (必需) - 显示名称
- `icon` (可选) - 分类图标，支持Emoji或图标文件
- `description` (可选) - 分类描述
- `sites` (可选) - 直接包含的网站列表
- `children` (可选) - 子分类列表，支持3级嵌套
- `order` (可选) - 排序权重，数字越小越靠前

**网站字段**：

- `id` (必需) - 唯一标识符
- `name` (必需) - 网站名称
- `url` (可选) - 网站链接，支持与markdownFile同时存在
- `description` (可选) - 网站描述
- `icon` (可选) - 网站图标
- `tags` (可选) - 标签数组，用于搜索和分类
- `markdownFile` (可选) - Markdown文档路径，支持与url同时存在
- `featured` (可选) - 是否为推荐网站

#### 🔗 多链接卡片功能

每个网站卡片支持三种链接模式的灵活组合：

**情况一：仅网址链接**
```json
{
  "id": "example-site",
  "name": "示例网站",
  "description": "这是一个外部网站",
  "icon": "🌐",
  "url": "https://example.com",
  "tags": ["工具", "外部"]
}
```
- 点击卡片直接打开外部网址
- 卡片显示为外部链接样式（绿色边框指示器）

**情况二：仅文档链接**
```json
{
  "id": "local-doc",
  "name": "本地文档",
  "description": "这是一个本地Markdown文档",
  "icon": "📄",
  "markdownFile": "nav/data/docs/guide.md",
  "tags": ["文档", "指南"]
}
```
- 点击卡片直接预览Markdown文档
- 卡片显示为文档样式（橙色边框指示器）
- 右上角显示"📚 文档"徽章

**情况三：网址+文档双链接**
```json
{
  "id": "dual-link-site",
  "name": "双链接网站",
  "description": "同时支持外部访问和本地文档",
  "icon": "🔗",
  "url": "https://example.com",
  "markdownFile": "nav/data/docs/example-guide.md",
  "tags": ["工具", "文档"]
}
```
- 点击卡片主体打开外部网址
- 右上角显示"文档"指示器，点击预览Markdown文档
- 卡片显示为混合样式（蓝色边框指示器）

**交互设计特点**：
- 🎯 **智能识别** - 系统自动根据字段配置判断卡片类型
- 🎨 **视觉区分** - 不同类型卡片使用不同颜色的边框指示器
- 👆 **精确点击** - 文档指示器支持独立点击，不影响主卡片功能
- 📱 **移动适配** - 紧凑模式下文档指示器自动调整为圆形图标
- ⚡ **性能优化** - 事件冒泡处理，避免点击冲突

#### 图标系统

支持多种图标类型：

```json
{
  "icon": "🔧",                          
  "icon": "assets/icons/github.svg",      
  "icon": "https://example.com/icon.svg",
  "icon": "https://favicon.ico"
}
```
icon：支持 `Unicode字符（Emoji）`、`本地SVG文件`、`远程图片链接`、`远程favicon`


**特性**：
- 智能识别图标类型
- 加载失败自动回退
- 支持懒加载优化
- 主题适配调整

#### Markdown文档

支持本地Markdown文档渲染，可与网址链接组合使用：

```json
{
  "id": "git-guide",
  "name": "Git使用指南",
  "description": "详细的Git命令和最佳实践",
  "icon": "📖",
  "markdownFile": "nav/data/docs/git-guide.md",
  "tags": ["Git", "教程", "文档"]
}
```

**Markdown功能特性**：
- 📖 **完整渲染** - 支持标题、列表、代码块、表格、链接等
- 🎨 **主题适配** - 文档样式自动适配当前主题
- 📱 **响应式** - 移动端优化的阅读体验
- ⚡ **快速加载** - 本地文档，无网络依赖

#### 🎯 多链接卡片使用场景

**开发工具类**：
```json
{
  "id": "vscode",
  "name": "Visual Studio Code",
  "description": "微软开发的免费代码编辑器",
  "icon": "💻",
  "url": "https://code.visualstudio.com",
  "markdownFile": "nav/data/docs/vscode-guide.md",
  "tags": ["编辑器", "开发工具"]
}
```
- 点击卡片：访问官网下载或使用
- 点击文档：查看使用指南和配置说明

**学习资源类**：
```json
{
  "id": "javascript-tutorial",
  "name": "JavaScript 教程",
  "description": "从入门到精通的JavaScript学习资源",
  "icon": "📚",
  "url": "https://javascript.info",
  "markdownFile": "nav/data/docs/js-notes.md",
  "tags": ["JavaScript", "教程", "前端"]
}
```
- 点击卡片：访问在线教程网站
- 点击文档：查看个人学习笔记和总结

**企业内部工具**：
```json
{
  "id": "internal-system",
  "name": "内部管理系统",
  "description": "公司内部使用的管理系统",
  "icon": "🏢",
  "url": "https://internal.company.com",
  "markdownFile": "nav/data/docs/system-manual.md",
  "tags": ["内部工具", "管理"]
}
```
- 点击卡片：访问内部系统
- 点击文档：查看使用手册和操作指南

#### 数据验证

使用以下脚本验证数据格式：

```javascript
// 在浏览器控制台运行
fetch('./data/sites.json')
  .then(r => r.json())
  .then(data => {
    console.log('✅ JSON格式正确');

    // 检查必需字段
    const errors = [];
    data.categories.forEach(cat => {
      if (!cat.id || !cat.name) {
        errors.push(`分类缺少必需字段: ${cat.name || cat.id}`);
      }

      if (cat.sites) {
        cat.sites.forEach(site => {
          if (!site.id || !site.name || !site.url) {
            errors.push(`网站缺少必需字段: ${site.name || site.id}`);
          }
        });
      }
    });

    if (errors.length === 0) {
      console.log('✅ 数据验证通过');
    } else {
      console.error('❌ 数据验证失败:', errors);
    }
  })
  .catch(e => console.error('❌ JSON格式错误:', e));
```

### ⏰ 时间范围提示配置

时间范围提示功能通过配置文件系统管理，支持在指定时间范围内显示首次提示。

#### 核心配置文件

**1. 应用配置 (`nav/data/appconfig.json`)**

```json
{
  "timeNotifications": {
    "enabled": true,
    "configPath": "data/time-notifications.json",
    "description": "时间范围提示配置",
    "autoLoad": true,
    "fallbackEnabled": false
  }
}
```

**配置属性说明**：
- `enabled`: 是否启用时间范围提示功能（布尔值）
- `configPath`: 时间提示配置文件的路径（字符串）
- `description`: 配置描述信息（字符串）
- `autoLoad`: 是否在应用启动时自动加载配置（布尔值）
- `fallbackEnabled`: 配置文件加载失败时是否启用降级配置（布尔值）

**2. 时间提示配置 (`data/time-notifications.json`)**

```json
{
  "version": "1.0",
  "config": {
    "enabled": true,
    "checkInterval": 30000,
    "defaultType": "info",
    "autoStart": true
  },
  "notifications": [
    {
      "id": "evening-reminder",
      "name": "晚间休息提醒",
      "type": "daily",
      "timeRange": {
        "start": "22:00",
        "end": "23:00"
      },
      "content": {
        "title": "晚间提醒",
        "message": "现在是晚间时段，建议适度使用电子设备，注意休息。",
        "type": "info"
      },
      "enabled": true,
      "description": "每日晚间休息提醒"
    }
  ],
  "presets": {
    "student": {
      "name": "学生作息",
      "description": "适合学生的作息提醒配置",
      "notifications": []
    },
    "office": {
      "name": "办公室工作",
      "description": "适合办公室工作的提醒配置",
      "notifications": []
    }
  }
}
```

**配置文件结构说明**：

**基础信息**：
- `version`: 配置文件版本号（字符串）
- `description`: 配置文件描述（可选，字符串）
- `lastUpdated`: 最后更新时间（可选，字符串）

**全局配置 (`config`)**：
- `enabled`: 是否启用时间范围提示功能（布尔值）
- `checkInterval`: 时间检查间隔，单位毫秒（数字，默认30000）
- `defaultType`: 默认提示类型（字符串：'info'|'success'|'warning'|'error'）
- `autoStart`: 是否自动开始检查（布尔值）

**通知配置 (`notifications`)**：
- `id`: 通知唯一标识符（字符串，必需）
- `name`: 通知显示名称（字符串，可选）
- `type`: 时间类型（字符串：'daily'|'absolute'）
- `timeRange`: 时间范围配置（对象）
  - `start`: 开始时间（字符串，格式见下方说明）
  - `end`: 结束时间（字符串，格式见下方说明）
- `content`: 提示内容配置（对象）
  - `title`: 提示标题（字符串，可选）
  - `message`: 提示消息（字符串，必需）
  - `type`: 提示类型（字符串：'info'|'success'|'warning'|'error'）
- `enabled`: 是否启用此通知（布尔值）
- `description`: 通知描述（字符串，可选）

**预设配置 (`presets`)**：
- 预设名称作为键（如 `student`、`office`）
- `name`: 预设显示名称（字符串）
- `description`: 预设描述（字符串）
- `notifications`: 预设包含的通知配置数组（数组）

#### 时间格式说明

**每日时间范围** (使用 HH:MM 格式)：
```json
{
  "type": "daily",
  "timeRange": {
    "start": "09:00",
    "end": "17:30"
  }
}
```
start：上午9点
end：下午5点30分

**属性说明**：
- `type`: 必须设置为 `"daily"`，表示每日重复的时间范围
- `start`: 开始时间，格式为 `HH:MM`（24小时制）
- `end`: 结束时间，格式为 `HH:MM`（24小时制）
- **特点**: 每天在指定时间范围内首次进入页面时显示提示

**跨天时间范围**：
```json
{
  "type": "daily",
  "timeRange": {
    "start": "23:30",
    "end": "02:00"
  }
}
```
start：晚上11点30分
end：次日凌晨2点

**属性说明**：
- `type`: 设置为 `"daily"`，支持跨天时间范围
- `start`: 开始时间（当天）
- `end`: 结束时间（次日），当 `end` < `start` 时自动识别为跨天
- **特点**: 系统自动处理跨天逻辑，如23:30-02:00表示从当天23:30到次日02:00

**绝对时间范围** (使用 ISO 8601 格式)：
```json
{
  "type": "absolute",
  "timeRange": {
    "start": "2025-07-29T20:00:00",
    "end": "2025-07-29T22:00:00"
  }
}
```

**属性说明**：
- `type`: 必须设置为 `"absolute"`，表示绝对时间范围
- `start`: 开始时间，ISO 8601格式 `YYYY-MM-DDTHH:MM:SS`
- `end`: 结束时间，ISO 8601格式 `YYYY-MM-DDTHH:MM:SS`
- **特点**: 在整个时间范围内只显示一次，适用于特定活动或事件提醒

#### 配置管理API

**配置文件操作**：

```javascript
// 查看当前配置 - 在控制台显示完整配置信息
TimeNotificationConfig.showConfig();
// 返回: void，在控制台输出配置详情

// 重新加载配置文件 - 从文件重新加载配置
await TimeNotificationConfig.reloadConfig();
// 返回: Promise<boolean> - 成功返回true，失败返回false

// 查看可用预设 - 获取所有预设配置列表
await TimeNotificationConfig.getAvailablePresets();
// 返回: Promise<Array> - 预设配置数组，每个元素包含 {id, name, description, notificationCount}

// 加载预设配置 - 应用指定的预设配置
await TimeNotificationConfig.loadPreset('student');  // 学生作息
await TimeNotificationConfig.loadPreset('office');   // 办公室工作
// 参数: presetName (string) - 预设名称
// 返回: Promise<boolean> - 成功返回true，失败返回false
```

**通知信息获取**：

```javascript
// 获取所有通知配置
const notifications = TimeNotificationConfig.getAllNotifications();
// 返回: Array - 所有通知配置的数组

// 获取启用的通知配置
const enabled = TimeNotificationConfig.getEnabledNotifications();
// 返回: Array - 仅包含enabled为true的通知配置
```

**运行时管理**：

```javascript
// 运行时添加通知
TimeNotificationConfig.addNotification({
  id: 'custom-reminder',           // 必需: 唯一标识符
  type: 'daily',                   // 必需: 'daily' 或 'absolute'
  timeRange: {                     // 必需: 时间范围配置
    start: '20:00',
    end: '20:30'
  },
  content: {                       // 必需: 提示内容配置
    title: '自定义提醒',           // 可选: 提示标题
    message: '这是一个自定义提醒',  // 必需: 提示消息
    type: 'info'                   // 可选: 提示类型，默认'info'
  },
  enabled: true                    // 可选: 是否启用，默认true
});
// 返回: string|null - 成功返回通知ID，失败返回null

// 移除通知
TimeNotificationConfig.removeNotification('notification-id');
// 参数: notificationId (string) - 要移除的通知ID
// 返回: boolean - 成功返回true，失败返回false

// 启用/禁用功能
TimeNotificationConfig.setEnabled(true);   // 启用
TimeNotificationConfig.setEnabled(false);  // 禁用
// 参数: enabled (boolean) - 是否启用

// 清除所有显示记录（重新触发提醒）
TimeNotificationConfig.clearAllRecords();
// 返回: void - 清除localStorage中的所有显示记录
```

#### 使用示例

**基础配置示例**：

**1. 每日时间范围提示**：
```javascript
// 每日时间范围提示 - 晚间休息提醒
TimeNotificationConfig.addNotification({
    id: 'evening-rest-reminder',        // 唯一标识符，用于管理此通知
    type: 'daily',                      // 每日重复类型
    timeRange: {
        start: '22:00',                 // 每天晚上10点开始
        end: '23:00'                    // 每天晚上11点结束
    },
    content: {
        title: '晚间休息提醒',          // 提示标题，显示在通知顶部
        message: '现在是晚间时段，建议适度使用电子设备，注意休息。',  // 提示内容
        type: 'info'                    // 提示类型：info(蓝色)、success(绿色)、warning(黄色)、error(红色)
    },
    enabled: true                       // 启用此通知
});
```

**2. 绝对时间范围提示**：
```javascript
// 绝对时间范围提示 - 特定活动提醒
TimeNotificationConfig.addNotification({
    id: 'special-event-2025',          // 特定事件的唯一标识
    type: 'absolute',                   // 绝对时间类型，只在指定时间段内有效
    timeRange: {
        start: '2025-07-29T20:00:00',   // 具体开始时间（ISO 8601格式）
        end: '2025-07-29T22:00:00'      // 具体结束时间（ISO 8601格式）
    },
    content: {
        title: '特别活动提醒',          // 活动标题
        message: '特别活动正在进行中，不要错过精彩内容！',  // 活动详情
        type: 'warning'                 // 使用警告样式突出显示
    },
    enabled: true                       // 启用此活动提醒
});
```

**3. 跨天时间范围提示**：
```javascript
// 跨天时间范围提示 - 深夜学习提醒
TimeNotificationConfig.addNotification({
    id: 'late-night-study',            // 深夜学习提醒标识
    type: 'daily',                      // 每日重复，但跨越两天
    timeRange: {
        start: '23:30',                 // 当天23:30开始
        end: '02:00'                    // 次日02:00结束（系统自动识别跨天）
    },
    content: {
        title: '深夜学习提醒',          // 健康提醒标题
        message: '深夜学习要注意身体健康，适当休息很重要。',  // 健康建议
        type: 'warning'                 // 使用警告样式提醒注意健康
    },
    enabled: true                       // 启用健康提醒
});
```

**配置属性详解**：
- `id`: 通知的唯一标识符，用于后续的管理操作（修改、删除等）
- `type`: 时间类型，`'daily'`表示每日重复，`'absolute'`表示特定时间段
- `timeRange.start`: 开始时间，daily类型使用HH:MM格式，absolute类型使用ISO 8601格式
- `timeRange.end`: 结束时间，格式同start，daily类型支持跨天（end < start）
- `content.title`: 可选的提示标题，显示在通知的顶部
- `content.message`: 必需的提示消息，通知的主要内容
- `content.type`: 提示样式类型，影响通知的颜色和图标
- `enabled`: 是否启用此通知，false时不会触发提示

#### 配置验证规则

**必需字段验证**：
- `id`: 必须是非空字符串，且在所有通知中唯一
- `type`: 必须是 `'daily'` 或 `'absolute'`
- `timeRange`: 必须包含 `start` 和 `end` 字段
- `content.message`: 必须是非空字符串

**时间格式验证**：
- **daily类型**: `start` 和 `end` 必须是 `HH:MM` 格式（如 `09:30`）
- **absolute类型**: `start` 和 `end` 必须是有效的 ISO 8601 格式（如 `2025-07-29T20:00:00`）
- **时间范围**: `end` 时间必须晚于 `start` 时间（daily类型支持跨天例外）

**可选字段默认值**：
- `name`: 默认使用 `id` 值
- `content.title`: 默认为空，不显示标题
- `content.type`: 默认为 `'info'`
- `enabled`: 默认为 `true`
- `description`: 默认为空

**快速设置函数**：

```javascript
// 学生作息提醒设置
function setupStudentSchedule() {
    const notifications = [
        {
            id: 'morning-class',
            timeRange: { start: '08:00', end: '08:30' },
            content: { title: '早课提醒', message: '早课时间到了，准备上课！', type: 'info' }
        },
        {
            id: 'sleep-reminder',
            timeRange: { start: '22:30', end: '23:00' },
            content: { title: '睡眠提醒', message: '该准备休息了，充足睡眠很重要！', type: 'warning' }
        }
    ];

    notifications.forEach(notification => {
        TimeNotificationConfig.addNotification({
            ...notification,
            type: 'daily',
            enabled: true
        });
    });
}

// 办公室工作提醒设置
function setupOfficeSchedule() {
    const notifications = [
        {
            id: 'work-start',
            timeRange: { start: '09:00', end: '09:30' },
            content: { title: '工作开始', message: '新的工作日开始，查看今天的任务！', type: 'success' }
        },
        {
            id: 'work-end',
            timeRange: { start: '18:00', end: '18:30' },
            content: { title: '下班时间', message: '工作结束，记得整理今天的工作成果。', type: 'success' }
        }
    ];

    notifications.forEach(notification => {
        TimeNotificationConfig.addNotification({
            ...notification,
            type: 'daily',
            enabled: true
        });
    });
}
```

### 支持的分类结构

```json
{
  "id": "parent-category",
  "name": "父分类",
  "icon": "🔧",
  "children": [
    {
      "id": "child-category",
      "name": "子分类",
      "sites": [
        {
          "id": "site-id",
          "name": "网站名称",
          "description": "网站描述",
          "icon": "🌐",
          "url": "https://example.com",
          "tags": ["标签1", "标签2"],
          "markdownFile": "nav/data/docs/site-guide.md"
        }
      ]
    }
  ]
}
```

## 🎨 自定义样式

### 修改主题色彩

编辑 `css/style.css` 中的CSS变量：

```css
:root {
    --primary-color: #3b82f6;      /* 主色调 */
    --background-color: #ffffff;    /* 背景色 */
    --text-primary: #1e293b;       /* 主文字色 */
    /* ... 更多变量 */
}
```

### 自定义深色主题

编辑 `css/themes.css` 中的深色主题变量：

```css
[data-theme="dark"] {
    --background-color: #0f172a;
    --text-primary: #f8fafc;
    /* ... 深色主题变量 */
}
```

### 🔗 自定义多链接卡片样式

编辑 `css/style.css` 中的卡片样式：

```css
/* 自定义外部链接卡片样式 */
.site-card-external {
    border-left: 3px solid #10b981;  /* 绿色边框 */
    background: linear-gradient(135deg, var(--card-background) 0%, #f0fdf4 100%);
}

/* 自定义文档卡片样式 */
.site-card-markdown {
    border-left: 3px solid #f59e0b;  /* 橙色边框 */
    background: linear-gradient(135deg, var(--card-background) 0%, #fffbeb 100%);
}

/* 自定义双链接卡片样式 */
.site-card-both {
    border-left: 3px solid #3b82f6;  /* 蓝色边框 */
    background: linear-gradient(135deg, var(--card-background) 0%, #eff6ff 100%);
}

/* 自定义文档指示器样式 */
.doc-indicator {
    background-color: #f59e0b;       /* 橙色背景 */
    color: white;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.doc-indicator:hover {
    background-color: #d97706;       /* 悬停时深橙色 */
    transform: scale(1.05);
}

/* 紧凑模式下的文档指示器 */
.compact-view .doc-indicator {
    padding: 2px 4px;
    border-radius: 50%;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.compact-view .doc-indicator span {
    display: none;  /* 隐藏文字，只显示图标 */
}
```

### 🎨 卡片类型指示器自定义

```css
/* 卡片右上角的类型指示器 */
.site-card::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    opacity: 0.6;
}

/* 不同类型的指示器颜色 */
.site-card-external::after {
    background-color: #10b981;  /* 绿色 - 外部链接 */
}

.site-card-markdown::after {
    background-color: #f59e0b;  /* 橙色 - 文档 */
}

.site-card-both::after {
    background-color: #3b82f6;  /* 蓝色 - 双链接 */
}

.site-card-no-link::after {
    background-color: #6b7280;  /* 灰色 - 无链接 */
    opacity: 0.3;
}
```

## 🔧 功能详解

### 🧭 智能导航系统

#### 多级分类结构

- **3级分类支持** - 主分类 → 子分类 → 具体分类的层级结构
- **智能展开逻辑** - 点击分类名称查看内容，点击箭头展开子分类
- **状态持久化** - 自动保存分类展开状态和当前浏览位置
- **父分类聚合** - 父分类显示所有子分类的网站内容

#### 访问统计管理

- **访问记录** - 自动记录网站访问次数和时间
- **使用频率分析** - 基于访问数据的智能排序
- **数据持久化** - 本地存储访问历史数据

### 🔍 高级搜索引擎

#### 搜索算法

- **多字段匹配** - 网站名称、描述、标签、分类名称、URL
- **智能权重排序** - 标题匹配(100分) > 描述匹配(40分) > 标签匹配(60分)
- **防抖优化** - 300ms防抖，减少不必要的搜索请求
- **实时高亮** - 搜索结果中匹配内容高亮显示

#### 交互体验

- **快捷键支持** - `⌘K`/`Ctrl+K` 快速唤起搜索
- **键盘导航** - 方向键选择结果，回车打开链接
- **匹配类型标识** - 显示匹配类型徽章（标题、描述、标签等）
- **搜索状态管理** - 智能的搜索状态切换和重置

### 🎨 多主题系统

#### 主题切换策略

**优先级顺序**：手动设置 > 时间自动 > 系统偏好 > 默认主题

#### 时间自动切换

- **智能时间检测** - 页面加载时根据当前时间判断主题
- **预设配置** - 标准、早起、夜猫子、办公等多种时间预设
- **自定义时间段** - 支持自定义浅色/深色主题时间段
- **无性能损耗** - 仅在页面加载时判断，不持续监听时间

#### 视觉效果

- **平滑过渡** - 主题切换时的平滑动画效果
- **防闪烁加载** - 页面加载时避免主题闪烁
- **一致性保证** - 所有UI组件的主题一致性

## 🎨 主题系统

### 🌈 可用主题

| 主题名称 | 主题ID | 设计理念 | 适用场景 |
|---------|--------|----------|----------|
| 日光象牙白 | `ivory-light` | 温暖舒适的浅色调 | 日间办公、阅读 |
| 夜月玄玉黑 | `dark-obsidian` | 深邃优雅的深色调 | 夜间使用、护眼 |
| 清雅茉莉绿 | `jasmine-green` | 禅意美学、护眼悦目 | 长时间使用 |
| 深邃海军蓝 | `navy-blue` | 沉稳专业的蓝色调 | 商务场景 |

### ⏰ 时间自动切换

#### 工作原理

- **页面加载判断** - 仅在页面加载时根据当前时间判断主题
- **性能优化** - 不持续监听时间变化，避免性能损耗
- **智能优先级** - 手动设置 > 时间自动 > 系统偏好 > 默认主题

#### 预设配置

| 预设名称 | 浅色时间段 | 深色时间段 | 适用人群 |
|---------|-----------|-----------|----------|
| `standard` | 6:00-18:00 | 18:00-6:00 | 标准作息 |
| `earlyBird` | 5:00-19:00 | 19:00-5:00 | 早起型用户 |
| `nightOwl` | 8:00-20:00 | 20:00-8:00 | 夜猫子型用户 |
| `office` | 9:00-17:00 | 17:00-9:00 | 办公时间 |

### 📱 快捷键支持

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `⌘K` / `Ctrl+K` | 唤起搜索 | 全局搜索快捷键 |
| `Escape` | 清空搜索/关闭侧边栏 | 重置界面状态 |
| `⌘Esc` / `Ctrl+Esc` | 重置主题配置 | 恢复默认主题设置 |
| `↑/↓` | 搜索结果导航 | 键盘选择搜索结果 |
| `Enter` | 选择搜索结果 | 打开选中的网站 |
| `?` | 显示帮助信息 | 查看当前主题和配置 |

**平台适配**：快捷键自动适配操作系统（macOS使用⌘键，Windows/Linux使用Ctrl键）

## 📚 API文档

### 🌐 全局接口

#### NavApp 主应用接口

```javascript
// 获取应用实例
const app = NavApp.getInstance()

// 刷新应用数据
NavApp.refresh()

// 获取应用统计信息
const stats = NavApp.getStats()

// 切换主题
NavApp.switchTheme()
```

#### 主题管理接口

```javascript
// 设置主题
NavApp.theme.setTheme('jasmine-green')

// 获取当前主题信息
const themeInfo = NavApp.theme.getInfo()
// 返回: { theme: "jasmine-green", source: "manual", description: "手动设置" }

// 重置主题配置
NavApp.theme.reset()

// 配置时间主题
NavApp.theme.configTime({
    enabled: true,
    lightStart: 7,
    lightEnd: 19
})
```

#### 搜索管理接口

```javascript
// 执行搜索
navApp.searchManager.performSearch('关键词')

// 获取搜索统计
const searchStats = NavApp.search.getStats()

// 重置标签筛选器提示
NavApp.search.resetTagHints()
```

#### 🔗 多链接卡片API

```javascript
// 检查卡片链接类型
function checkCardLinkType(siteData) {
    const hasUrl = siteData.url && siteData.url.trim() !== '';
    const hasMarkdown = siteData.markdownFile && siteData.markdownFile.trim() !== '';
    
    if (hasUrl && hasMarkdown) {
        return 'both';        // 双链接卡片
    } else if (hasMarkdown) {
        return 'markdown';    // 仅文档卡片
    } else if (hasUrl) {
        return 'external';    // 仅网址卡片
    } else {
        return 'none';        // 无链接卡片
    }
}

// 手动触发卡片点击事件
function triggerCardClick(cardElement, clickType = 'main') {
    const event = new MouseEvent('click', { bubbles: true });
    
    if (clickType === 'document') {
        // 模拟点击文档指示器
        const docIndicator = cardElement.querySelector('.doc-indicator');
        if (docIndicator) {
            docIndicator.dispatchEvent(event);
        }
    } else {
        // 模拟点击主卡片
        cardElement.dispatchEvent(event);
    }
}

// 获取卡片链接信息
function getCardLinkInfo(cardElement) {
    return {
        siteId: cardElement.dataset.siteId,
        siteName: cardElement.dataset.siteName,
        url: cardElement.dataset.url || null,
        markdownFile: cardElement.dataset.markdownFile || null,
        linkType: checkCardLinkType({
            url: cardElement.dataset.url,
            markdownFile: cardElement.dataset.markdownFile
        })
    };
}
```

### 🔧 快速配置API

#### 时间主题快速配置

```javascript
// 应用预设配置
applyTimeThemePreset("nightOwl")

// 自定义时间段
NavSphereQuickConfig.setTimeTheme(7, 19)

// 禁用时间主题
NavSphereQuickConfig.disableTimeTheme()

// 查看当前配置
NavSphereQuickConfig.showConfig()
```

#### 时间范围提示API

```javascript
// 配置文件管理
TimeNotificationConfig.showConfig()                    // 查看当前配置
await TimeNotificationConfig.reloadConfig()            // 重新加载配置文件
await TimeNotificationConfig.getAvailablePresets()     // 获取可用预设
await TimeNotificationConfig.loadPreset('student')     // 加载预设配置

// 运行时管理
TimeNotificationConfig.addNotification({...})          // 添加通知
TimeNotificationConfig.removeNotification('id')        // 移除通知
TimeNotificationConfig.setEnabled(true/false)          // 启用/禁用功能
TimeNotificationConfig.clearAllRecords()               // 清除显示记录

// 获取信息
TimeNotificationConfig.getAllNotifications()           // 获取所有通知
TimeNotificationConfig.getEnabledNotifications()       // 获取启用的通知
```

#### 调试和监控

```javascript
// 获取移动端调试信息
NavApp.debug()

// 查看应用状态
console.log(NavApp.getStats())

// 启用调试模式
window.NavSphereConfig.debug = true
```

## 🛠️ 开发指南

### 📋 开发环境

#### 环境要求

- **Node.js** 16+ (可选，用于开发工具)
- **现代浏览器** Chrome 80+, Firefox 75+, Safari 13+
- **代码编辑器** VS Code (推荐) 或其他现代编辑器
- **本地服务器** Python/Node.js/PHP 任一

#### 推荐工具

```json
{
  "vscode_extensions": [
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.live-server"
  ]
}
```

### 🏗️ 架构设计

#### 模块化架构

```text
┌─────────────────────────────────────────┐
│                NavApp                   │  ← 主应用控制器
├─────────────────────────────────────────┤
│  ThemeManager  │  SearchManager         │  ← 功能管理器
│  SidebarManager│  MarkdownManager       │
│  VisitManager  │  Platform Utils        │
├─────────────────────────────────────────┤
│  DOM操作层      │  事件处理层              │  ← 底层服务
│  数据处理层      │  工具函数层              │
└─────────────────────────────────────────┘
```

#### 核心模块职责

| 模块 | 职责 | 主要功能 |
|------|------|----------|
| `NavApp` | 主应用控制器 | 生命周期管理、模块协调、数据流控制 |
| `ThemeManager` | 主题管理 | 多主题切换、时间自动切换、系统偏好 |
| `SearchManager` | 搜索引擎 | 实时搜索、权重排序、键盘导航 |
| `SidebarManager` | 侧边栏管理 | 分类导航、状态记忆、移动端适配 |
| `MarkdownManager` | 文档渲染 | Markdown解析、模态框展示 |
| `VisitManager` | 访问统计 | 访问记录、使用频率分析 |

#### 数据流设计

```text
数据源 (sites.json) → 数据加载 → 数据处理 → 状态管理
                                                ↓
用户交互 ← 界面渲染 ← 状态更新 ← 事件处理 ← 界面渲染
```

### 🔧 开发实践

#### 代码规范

```javascript
/**
 * 搜索管理器 - 负责处理全局搜索功能
 * Search Manager - Handles global search functionality
 */
class SearchManager {
    /**
     * 执行搜索操作
     * @param {string} query 搜索关键词
     * @returns {Array} 搜索结果
     */
    performSearch(query) {
        // 实现搜索逻辑
    }
}
```

#### 设计模式应用

- **单例模式** - 主应用实例和管理器实例
- **观察者模式** - 主题变化通知和状态更新
- **策略模式** - 主题切换策略和搜索匹配策略
- **工厂模式** - DOM元素创建和事件处理器
- **模块模式** - 功能封装和命名空间管理

## � 部署方案

### 🌐 静态托管平台

#### GitHub Pages

```bash
# 1. 推送代码到仓库
git add .
git commit -m "Deploy First Share Navigation"
git push origin main

# 2. 启用GitHub Pages
# Settings → Pages → Source: Deploy from a branch → main
```

**优势**：免费、支持自定义域名、自动部署

#### Vercel

```bash
# 一键部署
npx vercel --prod
```

**优势**：全球CDN、自动HTTPS、边缘函数支持

#### Netlify

```bash
# CLI部署
npx netlify-cli deploy --prod --dir .
```

**优势**：表单处理、边缘函数、A/B测试

### 🖥️ 服务器部署

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/fs-oss-navigation;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_types
        text/css
        application/javascript
        application/json
        image/svg+xml;

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # HTML文件缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
}
```

#### Docker部署

```dockerfile
FROM nginx:alpine

# 复制文件
COPY . /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### ⚡ 性能优化

#### 资源优化

- **CSS压缩** - 生产环境压缩CSS文件
- **JavaScript压缩** - 压缩和混淆JS代码
- **图片优化** - 使用WebP格式，压缩图片大小
- **字体优化** - 使用系统字体，减少外部字体加载

#### 缓存策略

```javascript
// Service Worker缓存
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
```

#### 加载优化

- **懒加载** - 图片和非关键资源懒加载
- **预加载** - 关键资源预加载
- **防抖搜索** - 300ms防抖，减少搜索频率
- **事件委托** - 减少事件监听器数量

## 🔧 故障排除

### 🚨 常见问题

#### 页面无法加载

**症状**：白屏或加载失败

**解决方案**：

```bash
# 检查文件完整性
ls -la index.html css/ js/ data/

# 检查本地服务器
python -m http.server 8000

# 检查浏览器控制台错误
# F12 → Console 查看错误信息
```

#### 搜索功能异常

**症状**：搜索无结果或报错

**解决方案**：

```javascript
// 检查数据格式
fetch('./data/sites.json')
    .then(r => r.json())
    .then(data => console.log('✅ 数据加载成功:', data))
    .catch(e => console.error('❌ 数据加载失败:', e));

// 重新初始化搜索
navApp.searchManager.updateSearchData(navApp.allSites);
```

#### 主题切换问题

**症状**：主题不切换或样式错误

**解决方案**：

```javascript
// 清除主题缓存
localStorage.removeItem('navsphere-theme');

// 重置主题配置
NavApp.theme.reset();

// 检查主题配置
console.log(NavApp.theme.getInfo());
```

### 🛠️ 调试工具

#### 控制台命令

```javascript
// 显示应用信息
NavSphereQuickConfig.showConfig();

// 查看应用统计
console.log(NavApp.getStats());

// 测试搜索功能
navApp.searchManager.performSearch('test');

// 启用调试模式
window.NavSphereConfig.debug = true;
```

#### 性能分析

```javascript
// 搜索性能测试
function benchmarkSearch() {
    const queries = ['github', 'design', 'ai'];
    queries.forEach(query => {
        console.time(`Search: ${query}`);
        navApp.searchManager.performSearch(query);
        console.timeEnd(`Search: ${query}`);
    });
}

// 内存使用监控
console.log('Memory:', performance.memory);
```

### 代码结构详解

#### 主要类说明

**NavApp 主应用类**
```javascript
class NavApp {
    constructor() {
        this.data = null;              // 网站数据
        this.flatCategories = [];      // 扁平化分类
        this.allSites = [];           // 所有网站
        this.currentCategory = 'recommend'; // 当前分类

        // 管理器实例
        this.themeManager = null;
        this.searchManager = null;
        this.sidebarManager = null;
    }

    // 核心方法
    async init()                      // 初始化应用
    async loadData()                  // 加载数据
    processData()                     // 处理数据
    render()                          // 渲染界面
    switchCategory(categoryId)        // 切换分类
}
```

**ThemeManager 主题管理器**
```javascript
class ThemeManager {
    constructor() {
        this.timeThemeConfig = {      // 时间主题配置
            enabled: true,
            lightStart: 6,
            lightEnd: 18
        };
        this.currentTheme = 'light';  // 当前主题
    }

    // 核心方法
    getInitialTheme()                 // 获取初始主题
    toggleTheme()                     // 切换主题
    applyTheme(theme)                 // 应用主题
    getThemeByTime()                  // 根据时间获取主题
}
```

**SearchManager 搜索管理器**
```javascript
class SearchManager {
    constructor(navApp) {
        this.navApp = navApp;
        this.searchData = [];         // 搜索数据
        this.currentQuery = '';       // 当前查询
        this.selectedIndex = -1;      // 选中索引
    }

    // 核心方法
    performSearch(query)              // 执行搜索
    filterSites(sites, query)         // 过滤网站
    renderSearchResults(results)      // 渲染结果
    handleKeyboardNavigation(e)       // 键盘导航
}
```

### 添加新功能

#### 1. 添加新的网站分类
// 在 data/sites.json 中添加
```json
{
  "id": "new-category",
  "name": "新分类",
  "icon": "🆕",
  "sites": [
    {
      "id": "new-site",
      "name": "新网站",
      "description": "网站描述",
      "icon": "🌐",
      "url": "https://example.com",
      "tags": ["标签1", "标签2"]
    }
  ]
}
```

#### 2. 添加新的主题预设
```javascript
// 在 js/config.js 中添加
window.NavSphereConfig.timeTheme.presets.custom = {
    lightStart: 8,
    lightEnd: 20
};
```

#### 3. 自定义搜索逻辑
```javascript
// 扩展 SearchManager
SearchManager.prototype.customFilter = function(sites, query) {
    // 自定义搜索逻辑
    return sites.filter(site => {
        // 自定义匹配规则
        return site.name.includes(query);
    });
};
```

#### 4. 添加新的快捷键
```javascript
// 在相应管理器中添加
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'n') {
        // 自定义快捷键逻辑
        e.preventDefault();
        this.handleCustomShortcut();
    }
});
```

### 调试技巧

#### 1. 控制台调试
```javascript
// 查看应用状态
console.log(NavApp.getStats());

// 查看主题信息
console.log(NavApp.theme.getInfo());

// 查看搜索数据
console.log(navApp.searchManager.searchData);
```

#### 2. 性能分析
```javascript
// 测量加载时间
console.time('App Init');
// ... 应用初始化代码
console.timeEnd('App Init');

// 内存使用分析
console.log(performance.memory);
```

#### 3. 错误处理
```javascript
// 全局错误监听
window.addEventListener('error', (e) => {
    console.error('Global Error:', e.error);
});

// Promise错误监听
window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise:', e.reason);
});
```

## ⚡ 性能优化

### 已实现的优化

#### 1. 加载性能
- **静态资源**: 纯静态文件，无服务器依赖
- **CDN加速**: 外部资源使用CDN
- **资源压缩**: CSS/JS文件压缩
- **缓存策略**: 浏览器缓存和HTTP缓存头

#### 2. 运行时性能
- **防抖搜索**: 300ms防抖，减少搜索频率
- **虚拟滚动**: 大量数据时的性能优化（可选）
- **懒加载**: 图片和非关键资源懒加载
- **事件委托**: 减少事件监听器数量

#### 3. 内存优化
- **事件清理**: 组件销毁时清理事件监听器
- **数据缓存**: 合理的数据缓存策略
- **DOM复用**: 避免频繁创建/销毁DOM元素

### 性能监控

#### 使用内置工具
```javascript
// 查看应用性能统计
console.log(NavApp.getStats());

// 监控内存使用
console.log(performance.memory);

// 测量操作耗时
console.time('Search Performance');
// ... 搜索操作
console.timeEnd('Search Performance');
```

#### 性能基准测试
```javascript
// 搜索性能测试
function benchmarkSearch() {
    const queries = ['github', 'design', 'ai', 'tool'];
    const results = [];

    queries.forEach(query => {
        const start = performance.now();
        navApp.searchManager.performSearch(query);
        const end = performance.now();
        results.push({ query, time: end - start });
    });

    console.table(results);
}
```

### 优化建议

#### 1. 数据优化
- 控制 `sites.json` 文件大小（建议 < 1MB）
- 使用合适的图标（Emoji 或 SVG）
- 避免过长的描述文本

#### 2. 网络优化
```javascript
// 启用Service Worker缓存
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js');
}
```

#### 3. 代码分割
```javascript
// 动态导入非关键模块
async function loadAdvancedFeatures() {
    const { AdvancedSearch } = await import('./js/advanced-search.js');
    return new AdvancedSearch();
}
```

## 🔧 故障排除

### 常见问题

#### 1. 页面无法加载
**症状**: 白屏或加载失败
**原因**:
- 网络连接问题
- 服务器配置错误
- 文件路径错误

**解决方案**:
```bash
# 检查文件完整性
ls -la index.html css/ js/ data/

# 检查服务器日志
tail -f /var/log/nginx/error.log

# 使用浏览器开发者工具检查网络请求
```

#### 2. 搜索功能异常
**症状**: 搜索无结果或报错
**原因**:
- `sites.json` 格式错误
- JavaScript错误
- 数据加载失败

**解决方案**:
```javascript
// 检查数据格式
fetch('./data/sites.json')
    .then(r => r.json())
    .then(data => console.log('Data loaded:', data))
    .catch(e => console.error('Data error:', e));

// 重新初始化搜索
navApp.searchManager.updateSearchData(navApp.allSites);
```

#### 3. 主题切换问题
**症状**: 主题不切换或样式错误
**原因**:
- CSS文件加载失败
- 本地存储问题
- 时间配置错误

**解决方案**:
```javascript
// 清除主题缓存
localStorage.removeItem('navsphere-theme');
localStorage.removeItem('navsphere-theme-manual');

// 重置主题配置
NavApp.theme.reset();

// 检查时间主题配置
console.log(NavApp.theme.getTimeConfig());
```

#### 4. 移动端显示异常
**症状**: 布局错乱或功能失效
**原因**:
- CSS媒体查询问题
- 触摸事件处理错误
- 视口配置问题

**解决方案**:
```html
<!-- 检查视口配置 -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
```

```javascript
// 强制重新渲染
window.dispatchEvent(new Event('resize'));

// 检查移动端检测
console.log('Is mobile:', window.innerWidth < 768);
```

### 调试工具

#### 1. 控制台命令
```javascript
// 显示应用信息
NavSphereQuickConfig.showConfig();

// 测试搜索功能
navApp.searchManager.performSearch('test');

// 查看所有分类
console.log(navApp.flatCategories);

// 查看所有网站
console.log(navApp.allSites);
```

#### 2. 开发者工具
- **Console**: 查看错误和日志
- **Network**: 检查资源加载
- **Application**: 查看本地存储
- **Performance**: 性能分析
- **Lighthouse**: 性能评估

#### 3. 错误报告
```javascript
// 启用详细错误报告
window.NavSphereConfig.debug = true;

// 自定义错误处理
window.addEventListener('error', (e) => {
    console.group('🚨 Error Report');
    console.error('Message:', e.message);
    console.error('File:', e.filename);
    console.error('Line:', e.lineno);
    console.error('Stack:', e.error?.stack);
    console.groupEnd();
});
```

### 性能问题诊断

#### 1. 加载缓慢
```javascript
// 测量加载时间
window.addEventListener('load', () => {
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    console.log(`Page load time: ${loadTime}ms`);
});
```

#### 2. 内存泄漏
```javascript
// 监控内存使用
setInterval(() => {
    if (performance.memory) {
        console.log('Memory usage:', {
            used: Math.round(performance.memory.usedJSHeapSize / 1048576) + 'MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1048576) + 'MB'
        });
    }
}, 10000);
```

#### 3. 搜索性能
```javascript
// 搜索性能分析
function analyzeSearchPerformance() {
    const testQueries = ['github', 'design', 'ai', 'javascript', 'tool'];
    const results = [];

    testQueries.forEach(query => {
        const start = performance.now();
        const searchResults = navApp.searchManager.filterSites(navApp.allSites, query);
        const end = performance.now();

        results.push({
            query,
            results: searchResults.length,
            time: Math.round(end - start * 100) / 100 + 'ms'
        });
    });

    console.table(results);
}
```

## 🌍 浏览器兼容性

### 支持的浏览器
- ✅ **Chrome 80+** (推荐)
- ✅ **Firefox 75+**
- ✅ **Safari 13+**
- ✅ **Edge 80+**
- ⚠️ **IE 11** (部分功能受限)

### 使用的现代特性
- **CSS Grid 和 Flexbox**: 布局系统
- **CSS Variables**: 主题系统
- **ES6+ JavaScript**: 模块化代码
- **Fetch API**: 数据加载
- **LocalStorage**: 配置持久化
- **Intersection Observer**: 懒加载（可选）

### 兼容性处理

#### Polyfill 支持
```html
<!-- 为旧浏览器添加Polyfill -->
<script src="https://polyfill.io/v3/polyfill.min.js?features=fetch,Promise,Object.assign"></script>
```

#### 渐进增强
```javascript
// 功能检测
if ('IntersectionObserver' in window) {
    // 使用现代API
    enableLazyLoading();
} else {
    // 降级方案
    loadAllImages();
}
```

#### CSS兼容性
```css
/* 使用CSS变量，提供降级方案 */
.theme-button {
    background-color: #3b82f6; /* 降级方案 */
    background-color: var(--primary-color, #3b82f6);
}

/* Grid布局降级 */
.sites-container {
    display: flex; /* 降级方案 */
    flex-wrap: wrap;
    display: grid; /* 现代浏览器 */
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}
```

## 📄 许可证

MIT License - 可自由使用、修改和分发

## 🤝 贡献指南

### 📋 贡献方式

#### 问题反馈

- **Bug报告** - 使用GitHub Issues报告问题
- **功能建议** - 描述新功能需求和使用场景
- **文档改进** - 改进文档内容和示例

#### 代码贡献

**开发流程**：

```bash
# 1. Fork项目
git clone https://github.com/your-username/fs-oss-navigation.git
cd fs-oss-navigation

# 2. 创建功能分支
git checkout -b feature/your-feature-name

# 3. 开发和测试
# 编写代码、测试功能、检查兼容性

# 4. 提交更改
git add .
git commit -m "feat: 添加新功能描述"

# 5. 推送并创建PR
git push origin feature/your-feature-name
```

**代码规范**：

- 使用有意义的变量和函数名
- 中文注释业务逻辑，英文注释技术实现
- 保持代码风格一致性
- 确保向后兼容性

**提交信息规范**：

```bash
feat: 添加新功能        # 新功能
fix: 修复问题          # Bug修复
docs: 更新文档         # 文档更新
style: 样式调整        # 样式修改
refactor: 重构代码     # 代码重构
perf: 性能优化         # 性能改进
test: 测试相关         # 测试代码
```

### 🧪 测试指南

#### 功能测试

- [ ] 页面正常加载和渲染
- [ ] 搜索功能正常工作
- [ ] 主题切换正常
- [ ] 分类导航正常
- [ ] 移动端适配正常
- [ ] 快捷键功能正常

#### 浏览器兼容性

- [ ] Chrome (最新版)
- [ ] Firefox (最新版)
- [ ] Safari (最新版)
- [ ] Edge (最新版)
- [ ] 移动端浏览器

### 📞 联系方式

- **GitHub Issues** - 问题报告和功能讨论
- **Pull Requests** - 代码贡献和审查
- **Discussions** - 一般性讨论和问答

---

## 📄 许可证

MIT License - 可自由使用、修改和分发

## 📋 更新日志

### v2.1.0 (最新版本)
**🔗 多链接卡片功能**
- ✨ **新增** 每个卡片支持同时设置网址链接和文档链接
- ✨ **新增** 三种交互模式：仅网址、仅文档、网址+文档
- ✨ **新增** 智能文档指示器，支持独立点击
- ✨ **新增** 不同卡片类型的视觉区分（颜色边框指示器）
- 🎨 **优化** 紧凑模式下的文档指示器显示
- 🐛 **修复** 事件冒泡处理，避免点击冲突
- 📚 **文档** 完善的使用指南和API文档

**技术改进**
- 重构 `createSiteCard` 函数，支持多链接类型判断
- 优化 `handleSiteCardClick` 函数，支持精确事件处理
- 新增卡片类型样式类：`site-card-both`、`site-card-no-link`
- 完善CSS样式，支持文档指示器和类型指示器

### v2.0.0
**🚀 架构升级**
- 多文件数据源支持
- 时间范围提示系统
- 性能优化和代码重构

### v1.0.0
**🎉 首次发布**
- 基础导航功能
- 多主题系统
- 搜索和筛选
- 响应式设计

## 🔄 迁移指南

### 从 v2.0.x 升级到 v2.1.0

**数据配置更新**：
现有的 `sites.json` 配置无需修改，新功能向后兼容。如需使用多链接功能，可按以下方式更新：

```json
{
  "id": "existing-site",
  "name": "现有网站",
  "url": "https://example.com",
  "markdownFile": "nav/data/docs/site-guide.md"
}
```
markdownFile文档链接可为空，表示仅使用网址链接。

**样式兼容性**：
- 现有主题样式保持不变
- 新增的卡片类型样式自动生效
- 如有自定义样式，建议检查与新样式类的兼容性

**API变更**：
- 所有现有API保持兼容
- 新增多链接相关API，可选择性使用
- 事件处理逻辑优化，但接口保持一致

---

**FaciShare Navigation** - 让企业导航更智能、更美观、更高效！ 🚀

[![Star this repo](https://img.shields.io/github/stars/your-repo/fs-oss-navigation?style=social)](https://github.com/your-repo/fs-oss-navigation)
[![Fork this repo](https://img.shields.io/github/forks/your-repo/fs-oss-navigation?style=social)](https://github.com/your-repo/fs-oss-navigation/fork)
