{"categories": [{"id": "sr", "name": "应用发布", "icon": "🌈", "children": [{"id": "sr-app", "name": "服务发布", "icon": "⭐", "sites": [{"id": "old-nav", "name": "旧版导航页", "description": "当前导航页未找到跳转链接可切回旧版", "icon": "🚪", "url": "https://oss.foneshare.cn/navigation/", "tags": ["旧版导航页"]}, {"id": "k8s-app", "name": "发布系统", "description": "Kubernetes应用发布管理系统", "icon": "☸️", "url": "https://k8s-app.foneshare.cn/", "markdownFile": "nav/data/docs/k8s-deploy-guide.md", "tags": ["发布", "K8s", "部署", "运维"]}, {"id": "vm-deploy", "name": "发布系统(虚机应用)", "description": "虚拟机应用发布系统", "icon": "🖥️", "url": "https://oss.foneshare.cn/publish/deploy/", "tags": ["发布", "虚机", "部署", "运维"]}, {"id": "<PERSON>", "name": "<PERSON>", "description": "<PERSON>", "icon": "🖥️", "url": "https://oss.foneshare.cn/jenkins/"}]}, {"id": "sr-config", "name": "配置修改", "icon": "🔖", "sites": [{"id": "config-center-new", "name": "配置中心(新版)", "description": "新版配置管理中心", "icon": "⚙️", "url": "https://console.foneshare.cn/cms/", "tags": ["配置", "管理", "新版", "运维"]}, {"id": "config-center-old", "name": "配置中心(旧版)", "description": "旧版配置管理中心", "icon": "🔧", "url": "https://oss.foneshare.cn/cms/", "tags": ["配置", "管理", "旧版", "运维"]}, {"id": "cloud-control-center", "name": "云控中心", "description": "云控中心", "icon": "☁️", "url": "https://oss.foneshare.cn/cctrl-center/", "tags": ["云控", "控制", "管理"]}, {"id": "action-router", "name": "Action Router", "description": "灰度路由", "icon": "🔀", "url": "https://oss.foneshare.cn/router-console/", "tags": ["路由", "灰度", "管理"]}]}, {"id": "monitoring", "name": "日志监控", "icon": "📊", "sites": [{"id": "clickhouse-log-center", "name": "Clickhouse", "description": "基于Clickhouse的日志中心", "icon": "📊", "url": "https://log.foneshare.cn/", "tags": ["日志", "Clickhouse", "中心", "查询"]}, {"id": "grafana-log-center", "name": "Grafana日志导航", "description": "基于Grafana的日志中心", "icon": "📊", "url": "https://grafana.foneshare.cn/d/e8bf99ce-2bae-4b8c-a528-c12dced8f407/e8a786-e59bbe-e5afbc-e888aa-e9a1b5?orgId=1"}, {"id": "kibana-log-center", "name": "Kibana日志导航", "description": "基于Kibana的日志中心", "icon": "📊", "url": "https://oss.foneshare.cn/kibana01/app/dashboards"}, {"id": "app-log-monitor", "name": "应用日志", "description": "Grafana应用日志看板", "icon": "📝", "url": "https://grafana.foneshare.cn/d/ch-app-log/clickhouse-app-logs?orgId=1"}, {"id": "eye-service", "name": "蜂眼监控", "description": "蜂眼监控", "icon": "📝", "url": "https://oss.foneshare.cn/eye/service/"}, {"id": "fcd", "name": "终端可用率", "description": "终端可用率", "icon": "📝", "url": "https://oss.foneshare.cn/eye/service/"}, {"id": "socketlog", "name": "客户端日志", "description": "客户端日志", "icon": "📝", "url": "https://oss.foneshare.cn/eye/service/"}]}, {"id": "console", "name": "管理后台", "icon": "🔧", "sites": [{"id": "stone-admin", "name": "文件系统管理后台", "description": "文件系统管理后台", "icon": "🔧", "url": "https://oss.foneshare.cn/fs-stone-admin", "tags": ["stone", "文件系统", "管理后台"]}, {"id": "i18n-console", "name": "国际化管理平台", "description": "多语言国际化管理平台", "icon": "🌍", "url": "https://oss.foneshare.cn/i18n-console", "tags": ["国际化", "多语", "i18n", "管理后台"]}]}, {"id": "documentation", "name": "文档规范", "icon": "📚", "sites": [{"id": "release-spec", "name": "研发中心发布变更规范3.0", "description": "研发中心发布变更规范文档", "icon": "📋", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=103188954", "tags": ["发布", "规范", "变更", "研发"]}, {"id": "tech-spec", "name": "技术规范导航页", "description": "技术规范文档导航", "icon": "🧭", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=176134427", "tags": ["技术", "规范", "导航", "文档"]}, {"id": "devops-intro", "name": "纷享DevOps平台介绍", "description": "纷享DevOps平台使用介绍", "icon": "🔧", "url": "https://sharecrm.feishu.cn/wiki/wikcnR0NeujciC5EEtyOtnHMJdd", "tags": ["DevOps", "平台", "介绍", "纷享"]}, {"id": "log-platform", "name": "纷享日志平台-接入和查询", "description": "日志平台接入和查询指南", "icon": "📊", "url": "https://sharecrm.feishu.cn/wiki/wikcnpERP1FppAMp4s83g4ur4oc", "tags": ["日志", "平台", "接入", "查询"]}]}]}, {"id": "operations", "name": "运维管理", "icon": "⛱️", "sites": [{"id": "ops-platform", "name": "OpsPlatform", "description": "运维管理平台", "icon": "🛠️", "url": "https://mon.foneshare.cn/", "tags": ["运维", "管理", "平台", "监控"]}, {"id": "cmdb", "name": "CMDB", "description": "配置管理数据库", "icon": "📊", "url": "https://cmdb.foneshare.cn/index.php/", "tags": ["配置", "数据库", "管理", "CMDB"]}, {"id": "db-query", "name": "数据库查询", "description": "数据库查询", "icon": "🗄️", "url": "https://db-query.foneshare.cn/", "tags": ["MySQL", "数据库", "管理", "工具"]}, {"id": "kubepi", "name": "KubePi", "description": "Kubernetes集群管理工具", "icon": "☸️", "url": "https://oss.foneshare.cn/kubepi", "tags": ["Kubernetes", "集群", "管理", "工具"]}, {"id": "dubboAdmin", "name": "Dubbo-Admin", "description": "Dubbo服务管理控制台", "icon": "🔗", "url": "http://************:33380/", "tags": ["Dubbo", "服务", "管理", "fstest"]}, {"id": "rocketmqConsole", "name": "RocketMQ-Console", "description": "RocketMQ控制台", "icon": "🚀", "url": "http://oss.firstshare.cn/rmq-console-112/", "tags": ["RocketMQ", "消息队列", "监控", "MQ"]}, {"id": "kafka-manager", "name": "<PERSON><PERSON><PERSON>-Manager", "description": "Kafka消息队列管理工具(fsdevops)", "icon": "📨", "url": "https://oss.firstshare.cn/kafka-manager", "tags": ["Kafka", "消息队列", "管理", "fsdevops"]}, {"id": "kafka-log-center", "name": "Kafka-RedPanda", "description": "基于RedPanda的Kafka日志中心", "icon": "🐼", "url": "http://************:31190/", "tags": ["Kafka", "日志", "Red<PERSON>anda", "中心"]}, {"id": "cerebro", "name": "cerebro", "description": "Elasticsearch集群管理工具", "icon": "🍥", "url": "https://oss.foneshare.cn/cerebro/", "tags": ["Elasticsearch", "集群", "管理", "工具"]}]}, {"id": "code-related", "name": "代码管理", "icon": "🌀", "sites": [{"id": "git-repo", "name": "GitLab", "description": "代码版本管理仓库", "icon": "🦊", "url": "https://git.firstshare.cn/", "markdownFile": "nav/data/docs/git-guide.md", "tags": ["Git", "代码", "版本控制", "仓库"]}, {"id": "sourcegraph", "name": "SourceGraph", "description": "代码搜索和浏览工具", "icon": "🔍", "url": "https://sourcegraph.firstshare.cn/", "tags": ["代码搜索", "浏览", "SourceGraph", "Git"]}, {"id": "maven-repo", "name": "<PERSON><PERSON>", "description": "Maven依赖管理仓库", "icon": "📦", "url": "https://maven.firstshare.cn/", "tags": ["<PERSON><PERSON>", "依赖", "仓库", "Artifactory"]}, {"id": "code-scan", "name": "SonarQube", "description": "代码质量扫描工具", "icon": "🔎", "url": "https://oss.firstshare.cn/sonarqube/", "tags": ["代码扫描", "质量", "SonarQube", "检查"]}, {"id": "scaffold", "name": "SpringBoot脚手架", "description": "项目脚手架生成工具", "icon": "🏗️", "url": "https://oss.firstshare.cn/starter/", "tags": ["脚手架", "项目", "生成", "模板"]}, {"id": "parent-pom", "name": "父POM版本", "description": "父POM组件版本对比工具", "icon": "📋", "url": "https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md", "tags": ["POM", "版本", "组件", "对比"]}]}]}